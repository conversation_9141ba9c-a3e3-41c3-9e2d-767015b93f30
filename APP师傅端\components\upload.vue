<template>
    <view
        :class="[{'flex-warp':!imgclass || imgclass =='mini'},{'flex-center flex-column':imgclass && imgclass != 'mini'}]"
    >
        <block v-for="(item, index) in imagelist" :key="index">
            <view
                class="rel item-child"
                :class="[imgclass, {'margin': imgsize > 1}]"
                :style="radius ? 'border-radius:50%;overflow:hidden;' : ''"
            >
                <image
                    mode="aspectFill"
                    @tap="previewImage(item, imagelist)"
                    class="upload-img radius-16"
                    :src="item.path"
                    v-if="filetype == 'picture'"
                ></image>
                <video
                    :id="`video_${index}`"
                    class="upload-video rel radius-16"
                    :loop="false"
                    enable-play-gesture
                    enable-progress-gesture
                    :show-center-play-btn="true"
                    :controls="true"
                    :src="item.path"
                    :data-id="item.id"
                    objectFit="cover"
                    :data-index="index"
                    @play="onPlay"
                    @pause="onPause"
                    @ended="onEnded"
                    @timeupdate="onTimeUpdate"
                    @waiting="onWaiting"
                    @progress="onProgress"
                    @loadedmetadata="onLoadedMetaData"
                    v-if="filetype == 'video'"
                >
                    <cover-view
                        @tap="toDel(index)"
                        class="item-delete abs flex-center f-icontext c-base"
                        :style="{ background: primaryColor }"
                    >
                        删除
                    </cover-view>
                </video>

                <block v-if="filetype == 'picture'">
                    <view
                        @click="toDel(index)"
                        class="guanbi abs flex-center"
                        :class="[imgclass]"
                        style="z-index: 1;"
                        v-if="imgsize > 1"
                    >
                        <i class="iconfont icon-add rotate-45 c-base"></i>
                    </view>
                    <view
                        @click="chooseImage"
                        class="flex-center flex-column item-child upload-item radius-16 abs"
                        :class="[imgclass]"
                        style="top:0;margin-top:0;background:rgba(0,0,0,0.5);"
                        v-else
                    >
                        <view class="upload-icon flex-center c-title radius-10">
                            <i class="iconfont icon-camera"></i>
                        </view>
                        <view class="f-caption c-base mt-sm">重新上传</view>
                    </view>
                </block>
            </view>
        </block>
        <view
            @tap="chooseImage"
            class="radius-16 flex-center flex-column item-child upload-item fill-body"
            :class="[imgclass, {'margin': imgsize > 1}]"
            v-if="imagelist.length < imgsize"
        >
            <view class="upload-icon flex-center c-title radius-10">
                <i class="iconfont icon-camera"></i>
            </view>
            <view class="f-caption c-caption mt-sm" v-if="text">{{ text }}</view>
            <view class="cur-imgsize f-caption c-caption" v-if="imgsize > 1">{{ `${imagelist.length}/${imgsize}` }}</view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
    props: {
        imagelist: {
            type: Array,
            default() {
                return [];
            },
        },
        imgtype: {
            type: String,
            default() {
                return '';
            },
        },
        imgsize: {
            type: Number,
            default() {
                return 9;
            },
        },
        filetype: {
            type: String,
            default() {
                return 'picture';
            },
        },
        imgclass: {
            type: String,
            default() {
                return '';
            },
        },
        text: {
            type: String,
            default() {
                return '';
            },
        },
        radius: {
            type: Boolean,
            default() {
                return false;
            },
        },
    },
    computed: mapState({
        primaryColor: state => state.config.configInfo.primaryColor,
        subColor: state => state.config.configInfo.subColor,
    }),
    methods: {
        previewImage(current, urls) {
            let res_urls = [];
            urls = this.$util.deepCopy(urls);
            urls.forEach((item) => {
                res_urls.push(item.path);
            });
            uni.previewImage({
                current: current.path,
                urls: res_urls,
            });
        },
        async toDel(index) {
            let fileName = this.filetype == 'picture' ? '图片' : '视频';
            let [res_del, { confirm }] = await uni.showModal({
                content: `请确认是否要删除${fileName}`,
            });
            if (!confirm) return;
            let imagelist = this.$util.deepCopy(this.imagelist);
            imagelist.splice(index, 1);
            this.$emit('del', {
                imgtype: this.imgtype,
                imagelist,
            });
        },
        async chooseImage() {
            let { imgtype, imgsize, filetype } = this;
            let imagelist = this.$util.deepCopy(this.imagelist);
            let is_upload_img = filetype == 'picture';
            let chooseModel = is_upload_img ? 'chooseImage' : 'chooseVideo';
            let param = {
                count: imgsize - imagelist.length,
            };
            if (is_upload_img) {
                param.sizeType = ['compressed'];
            }

            let [res_upload, res_info] = await uni[chooseModel](param);
            if (res_upload) return;

            let { size = 0, tempFiles, tempFilePath = '' } = res_info;
            if (filetype == 'video' && size / 1024 / 1024 > 50) {
                this.$util.showToast({
                    title: `上传视频大小超过限制50M`,
                });
                return;
            }

            this.$util.showLoading({
                title: '上传中',
            });

            try {
                if (is_upload_img) {
                    for (let i = 0; i < tempFiles.length; i++) {
                        const response = await this.$api.base.uploadFile({
                            filePath: tempFiles[i].path,
                            name: 'multipartFile',
                            formData: {
                                type: this.filetype,
                            },
                        });

                        if (response) {
                            const path = response; // Adjust based on backend response
                            if (imgsize > 1) {
                                imagelist.push({ path });
                            } else {
                                imagelist = [{ path }];
                            }
                        } else {
                            throw new Error(response.msg || '上传失败');
                        }
                    }
                } else {
                    const response = await this.$api.base.uploadFile({
                        filePath: tempFilePath,
                        name: 'multipartFile',
                        formData: {
                            type: this.filetype,
                        },
                    });

                    if (response) {
                        const path = response;
                        imagelist.push({ path });
                    } else {
                        throw new Error(response.msg || '上传失败');
                    }
                }

                this.$util.hideAll();
                this.$emit('upload', {
                    imgtype,
                    imagelist,
                });
            } catch (error) {
                this.$util.hideAll();
                this.$util.showToast({
                    title: error.message || '上传失败，请重试',
                });
            }
        },
        onPlay(e) {},
        onPause(e) {},
        onEnded(e) {},
        onTimeUpdate(e) {},
        onWaiting(e) {},
        onProgress(e) {},
        onLoadedMetaData(e) {},
    },
};
</script>

<style lang="scss">
    .item-child {
        width: 216rpx;
        height: 216rpx;
        background: #f7f7f7;
        margin-bottom: 20rpx;
    }
    .margin {
        margin: 0 21rpx 21rpx 0;
    }
    .item-child:nth-child(3n) {
        margin-right: 0rpx;
    }
    .item-child.sm {
        width: 140rpx;
        height: 140rpx;
    }
    .item-child.mini {
        width: 196rpx;
        height: 196rpx;
    }
    .item-child.md {
        width: 335rpx;
        height: 210rpx;
        margin: 0rpx;
    }
    .item-child.lg {
        width: 686rpx;
        height: 400rpx;
    }
    .upload-img,
    .upload-video {
        width: 100%;
        height: 100%;
    }
    .upload-video {
        .item-delete {
            width: 60rpx;
            height: 32rpx;
            top: 0;
            right: 0;
            z-index: 2;
        }
    }
    .upload-item {
        .upload-icon {
            width: 80rpx;
            height: 76rpx;
            background: #ffffff;
            .iconfont {
                font-size: 40rpx;
                display: block;
            }
        }
        .cur-imgsize {
            line-height: 1.1;
        }
    }
    .upload-item.margin {
        margin-bottom: 0;
    }
    .guanbi {
        width: 32rpx;
        height: 32rpx;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 0 15rpx 0 0;
        top: 0rpx;
        right: 0rpx;
        z-index: 1;
        .iconfont {
            font-size: 28rpx;
        }
    }
    .guanbi.lg {
        width: 50rpx;
        height: 50rpx;
        .iconfont {
            font-size: 38rpx;
        }
    }
</style>