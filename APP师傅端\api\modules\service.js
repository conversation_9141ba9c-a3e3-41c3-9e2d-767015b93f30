import {
	req
} from '../../utils/req.js';
export default {
	// 首页轮播图
	// index(param) {
	// 	return req.get(`/massage/app/Index/index`)
	// },
	index(param) {
		return req.get(`home/index`)
	},
	// 服务列表
	serviceList(param) {
		return req.get("massage/app/Index/serviceList", param)
	},
	//公告中心
	messageList(param) {
		return req.get("home/notice", param)
	},//抽成
	commissionRatio(param) {
		return req.get("service/commissionRatio", param)
	},
	// 服务详情
	// serviceInfo(param) {
	// 	return req.get("/massage/app/Index/serviceInfo", param)
	// },
	serviceInfo(param) {
		return req.get(`service/info/${param}`)
	},
	// 首页详情
	getBottom(param) {
		return req.get(`home/serviceCate?cityName=${param}`)
	},
	orderGoodInfo(param) {
		return req.get(`service/info/${param}`)
	},
	getagents(param) {
		const { cityName,pageNum = 1,serviceId=521, pageSize =5,latitude=33.038799,longitude=115.277} = param;
		return req.get(`agents/list?cityName=${cityName}&serviceId=${serviceId}&pageNum=${pageNum}&pageSize=${pageSize}&latitude=${latitude}&longitude=${longitude}`)
	},
	getHighlight(param){
		return req.post(`order/readStatus`, param)
	},
	updataHighlight(param){
		return req.post("order/updateReadStatus", param)
	},
	// 去售后
	submitAfterSales(param){
		return req.post("order/aftermarket", param)
	},
	// 服务技师列表(ser_id，服务id,lat,lng)
	serviceCoachList(param) {
		return req.get("/massage/app/Index/serviceCoachList", param)
	},
	// 技师服务列表(coach_id)
	coachServiceList(param) {
		return req.get("/massage/app/Index/coachServiceList", param)
	},
	// 技师服务列表(coach_id)
	commentList(param) {
		return req.get("/massage/app/Index/commentList", param)
	},
	// 技师信息
	coachInfo(param) {
		return req.get("/massage/app/Index/coachInfo", param)
	},
	//优惠券列表
	couponList(param) {
		return req.get("/massage/app/Index/couponList", param)
	},
	//领取优惠券
	userGetCoupon(param) {
		return req.post("/massage/app/Index/userGetCoupon", param)
	},
	//获取一口价/比价配置
	getPz(param) {
		return req.post("order/priceSetting/list", param)
	},
	// getPz(param) {
	// 	return req.post("order/priceSetting/submit", param)
	// },
	//提交一口价/比价配置
	// postPz(param) {
	// 	return req.post("/massage/app/IndexOrder/priceSettingSubmit", param)
	// },
	postPz(param) {
		return req.post("order/priceSetting/submit", param)
	},
	postHuoDong(param) {
		return req.post("activityOrder/condClearPriceSetting/submit", param)
	},
	huodongcount(param) {
		return req.get("activityOrder/selectCount", param)
	},
	huodongquxiao(param) {
		return req.post("activityOrder/closeActivityType", param)
	},
	huodongorder(param) {
		return req.get("activityOrder/selectActivity", param)
	},
	huodongqueding(param) {
		return req.post("activityOrder/completedActivityOrder", param)
	},
	huodongpingjia(param) {
		return req.post("activityOrder/commentActivityOrder", param)
	},

	huodongselectActivityConfig(param) {
		return req.get("activityOrder/selectActivityConfig", param)
	},
	// //优惠券列表
	// getWelfareList(param) {
	// 	return req.get("/massage/app/Index/couponList", param)
	// },
	//优惠券列表
	getWelfareList(param) {
		const { userId,pageNum = 1, pageSize = 100} = param;
		console.log(userId)
		return req.get(`coupon/list?userId=${userId}&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	// 去上门qushangmenapi
	qushangmenapi(param) {
		return req.post("shiFu/orderDetails/selectOrderType", param)
	},
	//领取优惠券
	// getWelfareuser(param) {
	// 	return req.post("coupon/getCoupon", param)
	// },
	// getWelfare(param) {
	// 	return req.post("/massage/app/Index/userGetCoupon", param)
	// },f
	//我的优惠券const { payType, isSpread = 0, pageNum = 1, pageSize = 10 } = param;
		 
	myWelfare(param) {
		const {payPrice=0,status=1, serviceId=-1,pageNum = 1, pageSize = 10 } = param;
		 return req.get(`coupon/userCouponList?status=${status}&payPrice=${payPrice}&serviceId=${serviceId}&pageNum=${pageNum}&pageSize=${pageSize}`);
	},
	getWelfare(param){
			return req.post("coupon/getCoupon", param)
	},
	// myUserFare(param) {
	// 	return req.get(`coupon/userCouponList/${param}`)
	// },
	//订单提交
	// subOrder(param) {
	// 	return req.post("/massage/app/IndexOrder/payOrderShifu", param)
	// },
	subOrder(param) {
		return req.post("order/payOrderShifu", param)
	},
	subHuoOrder(param) {
		return req.post("activityOrder/conditionerCleaning", param)
	},
	//立即支付
	// nowPay(param) {
	// 	return req.get("/massage/app/IndexOrder/orderPay", param)
	// },
	nowPay(param) {
		return req.post("order/orderPay", param)
	},
	huodongPay(param) {
		return req.post("activityOrder/activityOrderPay", param)
	},
	//师傅入驻
	masterIn(param) {
		return req.post("/massage/app/IndexUser/coachApply", param)
	},
	//师傅押金缴纳
	masterPayY(param) {
		return req.get("/massage/app/IndexUser/cashPledge", param)
	},

	//师傅信息查看
	masterSee(param) {
		return req.get("coach/coachInfo", param)
	},
	//代理商申请
	// dlApply(param) {
	// 	return req.post("/massage/app/IndexUser/commissionMerchantApply", param)
	// },
	dlApply(param) {
		return req.post("user/commissionMerchantApply", param)
	},

	
	//代理商查看
	dlSee(param) {
		return req.get("/massage/app/IndexUser/commissionMerchantInfo", param)
	},
	//我的订单
	myOrder(param) {
		return req.get("/massage/app/IndexOrder/orderList", param)
	},
	// userOrder(param) {f
	// 	// return req.get(`order/list?payType=${param}`)
	// 	const { payType, isSpread = 0 } = param;
	// 	return req.get(`order/list?payType=${payType}&isSpread=${isSpread}`)
	// },
	userOrder(param) {
		// return req.get(`order/list?payType=${param}`)
		const {payType, isSpread = 0, pageNum = 1, pageSize = 10 } = param;
		  return req.get(`order/list?payType=${payType}&isSpread=${isSpread}&pageNum=${pageNum}&pageSize=${pageSize}`);
	},
	promotionUser(param) {
		// return req.get(`order/list?payType=${param}`)
		const {type=1, pageNum = 1, pageSize = 10 } = param;
		  return req.get(`user/orderList?pageNum=${pageNum}&pageSize=${pageSize}&type=${type}`);
		  // return req.get(`user/orderList?pageNum=${pageNum}&pageSize=${pageSize}`);
	},
	// userOrder(param) {
	// 	// return req.get(`order/list?payType=${param}`)
	// 	return req.get(`order/list?payType=${payType}&isSpread=${isSpread}`)
	// },
	//取消订单
	// cancelOrder(param) {
	// 	return req.post("/massage/app/IndexOrder/cancelOrder", param)
	// },
	cancelOrder(param) {
		return req.post("order/cancelOrder", param)
	},
	//确认完成订单
	confirmOrder(param) {
		return req.post("order/updateOrder", param)
	},
	//接单大厅
	receiving(param) {
		return req.get("/massage/app/IndexCoach/allOrderList", param)
	},
	//我的订单（师傅端）
	master_Order(param) {
		return req.get("/massage/app/IndexCoach/orderList", param)
	},
	//师傅接单（一口价）
	rece_Order(param) {
		return req.get("/massage/app/IndexCoach/acceptOrder", param)
	},
	//报价，修改报价，取消报价
	updateBao(param) {
		return req.post("/massage/app/IndexCoach/quotedPrice", param)
	},
	//我的报价列表（师傅端）
	masterBaolist(param) {
		return req.get("/massage/app/IndexCoach/quotedPriceList", param)
	},
	//选择报价（用户端）
	// choosePrice(param) {
	// 	return req.get("/massage/app/IndexOrder/chooseCoach", param)
	// },
	choosePrice(param) {
		return req.post("coach/chooseCoach", param)
	},
	//订单详情（用户端）
	orderdet(param) {
		return req.get("/massage/app/IndexOrder/orderInfo", param)
	},
	//订单详情（师傅端）
	// orderdetM(param) {
	// 	return req.get("/massage/app/IndexCoach/orderInfo", param)
	// },
	orderdet(param) {
		return req.get(`order/info/${param}`)
	},
	//师傅信息（师傅端）
	masterInfo(param) {
		return req.get("/massage/app/IndexUser/coachInfo", param)
	},
	//保存师傅信息（师傅端）
	saveMasterInfo(param) {
		return req.post("/massage/app/IndexCoach/coachUpdateV2", param)
	},
	//服务收入（师傅端）
	Masterget(param) {
		return req.get("/massage/app/IndexUser/financeLog", param)
	},
	//分类（用户端）
	// serviceCate(param) {
	// 	return req.get(`/massage/app/Index/serviceCate&city_id=${param}`)
	// },
	serviceCate(param) {
		return req.get(`service/serviceCate?cityName=${param}`)
	},
	userInfoConfig(param) {
		return req.get(`home/config`)
	},
	//加入购物车（用户端）
	// addtocar(param) {
	// 	return req.post("/massage/app/Index/addCar", param)
	// },
	addtocar(param) {
		return req.post("cart/add", param)
	},
	//查看购物车（用户端）
	// seecar(param) {
	// 	return req.get("/massage/app/Index/carlist", param)
	// },
	seecar(param) {
		return req.get("cart/list", param)
	},
	//减少购物车（用户端）
	// discar(param) {
	// 	return req.post("/massage/app/Index/delCar", param)
	// },
	discar(param) {
		return req.delete(`cart/del/${param}`)
	},
	//申请退款（用户端）
	applyTui(param) {
		return req.post("order/refundOrder", param)
	},
	//发表评价（用户端）
	// addeva(param) {
	// 	return req.post("/massage/app/IndexOrder/addComment", param)
	// },
	addeva(param) {
		return req.post("service/addComment", param)
	},
	//推广而为按摩（用户端）
	// getEwm(param) {
	// 	return req.get("/massage/app/IndexUser/userCommQr", param)
	// },
	getUserQr(param) {
		return req.get("user/userCommQr", param)
	},
	getUserQr(param) {
		return req.get("user/userCommQr", param)
	},
	getImgdev(param) {
		return req.get("file/downloadFile", param)
	},
	//注册（用户端）
	getZc(param) {
		return req.post("/index/login/reg", param)
	},
	//手机验证码（用户端）
	getYzm(param) {
		return req.post("/massage/app/IndexUser/sendShortMsg", param)
	},
	//配置信息(用户端)
	getConfig(param) {
		return req.get("home/config", param)
	},
	//修改个人信息(用户端)
	getSaveInfo(param) {
		return req.post("/massage/app/IndexUser/userUpdate", param)
	},
	//省市区(用户端)
	// getCity(param) {
	// 	return req.get(`/massage/app/IndexCommon/citySelect&pid=${param}`)
	// },

	getCity(param) {
		return req.get(`city/citySelect?pid=${param}`)
	},
	getuserCity(param) {
		return req.get(`city/citySelect`)
	},
	//项目评价列表(用户端)
	getserviceeva(param) {
		return req.get(`/massage/app/Index/commentListByGoods&service_id=${param}`)
	},
	getServiceCate(param) {
		return req.get(`service/comment/${param}`)
	},
	//邀请人员列表(用户端)
	// getyqlist(param) {
	// 	return req.get(`/massage/app/IndexUser/myTeam&page=${param}`)
	// },
	getMyTeam(param) {
		const { type=1,  pageNum = 1, pageSize = 10 } = param;
		  return req.get(`user/myTeam?pageNum=${pageNum}&pageSize=${pageSize}&type=${type}`);
		// return req.get(`user/myTeam`)
	},
	//热门搜索(用户端)
	getHotSearch(param) {
		return req.get(`/massage/app/IndexOrder/hotKeywords`)
	},
	//手机号登陆(用户端)
	getloginphone(param) {
		return req.post(`/index/login/login`, param)
	},
	//查看用户信息(用户端)
	getuserInfo(param) {
		return req.get(`/massage/app/IndexUser/userInfo`, param)
	},
	//添加银行卡(用户端)
	addbankcard(param) {
		return req.post(`/massage/app/IndexUser/bankCardAdd`, param)
	},
	addcard(param) {
		return req.post(`card/bankCardAdd`, param)
	},
	//银行卡列表(用户端)
	getbankcardlist(param) {
		return req.get(`card/bankCardList`, param)
	},
	getuserCardlist(param) {
		return req.get(`card/bankCardList`, param)
	},
	//删除银行卡(用户端)
	delbankcardlist(param) {
		return req.get(`/massage/app/IndexUser/bankCardDel&id=${param}`)
	},
	delcardlist(param) {
		return req.delete(`card/bankCardDel/${param}`)
	},
	//查看保证金(用户端)
	seeBzj(param) {
		return req.get(`/massage/app/IndexUser/cashPledgeView`, param)
	},
	//选择城市(用户端)
	selectCity(param) {
		return req.get(`/massage/app/IndexCommon/selectCity`, param)
	},
	//所有城市(用户端)
	allCity(param) {
		return req.get(`/massage/app/IndexCommon/allCity`, param)
	},
	//热门(用户端)
	hotCity(param) {
		return req.get(`/massage/app/IndexCommon/hotcity`, param)
	},
	//分销佣金流水(用户端)
	// water(param) {
	// 	return req.get(`/massage/app/IndexUser/commList`, param)
	// },
	// userWater(param) {
	// 	return req.get(`user/commList`, param)
	// },
	userWater(param) {
		const { pageNum = 1, pageSize = 10 } = param;
		return req.get(`user/newCommList?type=1,3&pageNum=${pageNum}&pageSize=${pageSize}`)
	},
	//第三方列表(用户端)
	threeList(param) {
		return req.get(`/massage/app/Index/otherCompany`, param)
	},
	//推广人订单(用户端)
	tuiList(param) {
		return req.get(`/massage/app/IndexOrder/orderListByUser`, param)
	},
	//我的推广(用户端)
	seeTuiMoney(param) {
		return req.get(`user/userCashInfo`, param)
	},
	tgIndex(param) {
		return req.get(`user/userCashInfo`, param)
	},
	//获取验证码
	getShortCode(param) {
		return req.post(`/massage/app/IndexUser/sendShortMsg`, param)
	},
	//手机号注册
	phoneRegister(param) {
		return req.post(`/index/login/reg`, param)
	},
	//手机号登录
	phoneLogin(param) {
		return req.post(`/index/login/login`, param)
	},
	//apple登录
	iphoneLogin(param) {
		return req.post(`/index/appleLogin`, param)
	},
	//账号注销
	cancellation(param) {
		return req.get(`/massage/app/IndexUser/logoff`, param)
	},
	//修改密码
	forget(param) {
		return req.post(`/index/forgetPwd`, param)
	},
}